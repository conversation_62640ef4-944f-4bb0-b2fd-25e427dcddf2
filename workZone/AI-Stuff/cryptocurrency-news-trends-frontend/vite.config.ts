import react from '@vitejs/plugin-react'
import path from 'path'
import { defineConfig } from 'vite'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    react({
      // React 插件配置 - 使用默认配置即可
    })
  ],

  // 路径别名配置 - 与 Taro config 保持一致
  resolve: {
    alias: {
      '@': path.resolve(__dirname, 'src'),
      '@/components': path.resolve(__dirname, 'src/components'),
      '@/pages': path.resolve(__dirname, 'src/pages'),
      '@/utils': path.resolve(__dirname, 'src/utils'),
      '@/services': path.resolve(__dirname, 'src/services'),
      '@/store': path.resolve(__dirname, 'src/store'),
      '@/assets': path.resolve(__dirname, 'src/assets'),
      '@/hooks': path.resolve(__dirname, 'src/hooks')
    },
    extensions: ['.js', '.jsx', '.ts', '.tsx', '.mjs', '.vue']
  },

  // 开发服务器配置
  server: {
    host: '0.0.0.0',
    port: 10086,
    open: true,
    hmr: true
  },

  // 构建配置
  build: {
    target: 'es2015',
    outDir: 'dist',
    assetsDir: 'static',
    sourcemap: false,
    minify: 'terser',
    chunkSizeWarningLimit: 1000,
    rollupOptions: {
      output: {
        chunkFileNames: 'js/[name].[hash:8].js',
        entryFileNames: 'js/[name].[hash:8].js',
        assetFileNames: 'assets/[name].[hash:8][extname]',
        manualChunks: {
          // 将第三方库分离到单独的 chunk
          vendor: ['react', 'react-dom'],
          mui: ['@mui/material', '@mui/icons-material'],
          utils: ['axios', 'zustand']
        }
      }
    },
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true
      }
    }
  },

  // CSS 配置
  css: {
    postcss: './postcss.config.js',
    modules: {
      localsConvention: 'camelCase'
    },
    preprocessorOptions: {
      scss: {
        // additionalData: `@import "@/styles/variables.scss";` // 如果有全局样式变量文件可以取消注释
      },
      stylus: {
        // additionalData: `@import "@/styles/variables.styl"` // 如果有全局样式变量文件可以取消注释
      }
    }
  },

  // 环境变量配置
  define: {
    __DEV__: process.env.NODE_ENV === 'development'
  },

  // 优化配置
  optimizeDeps: {
    include: [
      'react',
      'react-dom',
      '@mui/material',
      '@mui/icons-material',
      'axios',
      'zustand'
    ]
  }
})
