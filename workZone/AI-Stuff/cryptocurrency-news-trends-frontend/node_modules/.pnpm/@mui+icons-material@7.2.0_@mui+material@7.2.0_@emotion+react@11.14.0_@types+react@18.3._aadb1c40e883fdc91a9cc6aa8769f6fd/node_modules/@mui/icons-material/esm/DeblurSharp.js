"use client";

import createSvgIcon from "./utils/createSvgIcon.js";
import { jsx as _jsx } from "react/jsx-runtime";
export default createSvgIcon([/*#__PURE__*/_jsx("path", {
  d: "M12 3v18c4.97 0 9-4.03 9-9s-4.03-9-9-9"
}, "0"), /*#__PURE__*/_jsx("circle", {
  cx: "6",
  cy: "14",
  r: "1"
}, "1"), /*#__PURE__*/_jsx("circle", {
  cx: "6",
  cy: "18",
  r: "1"
}, "2"), /*#__PURE__*/_jsx("circle", {
  cx: "6",
  cy: "10",
  r: "1"
}, "3"), /*#__PURE__*/_jsx("circle", {
  cx: "3",
  cy: "10",
  r: ".5"
}, "4"), /*#__PURE__*/_jsx("circle", {
  cx: "6",
  cy: "6",
  r: "1"
}, "5"), /*#__PURE__*/_jsx("circle", {
  cx: "3",
  cy: "14",
  r: ".5"
}, "6"), /*#__PURE__*/_jsx("circle", {
  cx: "10",
  cy: "21",
  r: ".5"
}, "7"), /*#__PURE__*/_jsx("circle", {
  cx: "10",
  cy: "3",
  r: ".5"
}, "8"), /*#__PURE__*/_jsx("circle", {
  cx: "10",
  cy: "6",
  r: "1"
}, "9"), /*#__PURE__*/_jsx("circle", {
  cx: "10",
  cy: "14",
  r: "1.5"
}, "10"), /*#__PURE__*/_jsx("circle", {
  cx: "10",
  cy: "10",
  r: "1.5"
}, "11"), /*#__PURE__*/_jsx("circle", {
  cx: "10",
  cy: "18",
  r: "1"
}, "12")], 'DeblurSharp');