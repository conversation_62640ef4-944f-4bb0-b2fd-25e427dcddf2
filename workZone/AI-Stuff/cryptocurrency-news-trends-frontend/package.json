{"name": "cryptocurrency-news-trends-frontend", "version": "1.0.0", "description": "A cryptocurrency news trends analysis frontend application", "main": "index.js", "scripts": {"build:weapp": "node --max-old-space-size=4096 ./node_modules/.bin/taro build --type weapp", "build:swan": "node --max-old-space-size=4096 ./node_modules/.bin/taro build --type swan", "build:alipay": "node --max-old-space-size=4096 ./node_modules/.bin/taro build --type alipay", "build:tt": "node --max-old-space-size=4096 ./node_modules/.bin/taro build --type tt", "build:h5": "node --max-old-space-size=4096 ./node_modules/.bin/taro build --type h5", "build:rn": "node --max-old-space-size=4096 ./node_modules/.bin/taro build --type rn", "build:qq": "node --max-old-space-size=4096 ./node_modules/.bin/taro build --type qq", "build:jd": "node --max-old-space-size=4096 ./node_modules/.bin/taro build --type jd", "dev:weapp": "npm run build:weapp -- --watch", "dev:swan": "npm run build:swan -- --watch", "dev:alipay": "npm run build:alipay -- --watch", "dev:tt": "npm run build:tt -- --watch", "dev:h5": "npm run build:h5 -- --watch", "dev:rn": "npm run build:rn -- --watch", "dev:qq": "npm run build:qq -- --watch", "dev:jd": "npm run build:jd -- --watch", "lint": "eslint src --ext .ts,.tsx,.js,.jsx", "lint:fix": "eslint src --ext .ts,.tsx,.js,.jsx --fix", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,scss,md}\"", "type-check": "tsc --noEmit"}, "browserslist": ["last 3 versions", "Android >= 4.1", "ios >= 8"], "author": "", "license": "MIT", "dependencies": {"@babel/runtime": "^7.21.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@mui/icons-material": "^7.2.0", "@mui/lab": "7.0.0-beta.14", "@mui/material": "^7.2.0", "@tarojs/components": "4.0.7", "@tarojs/helper": "4.0.7", "@tarojs/plugin-framework-react": "^4.1.4", "@tarojs/plugin-html": "^4.1.4", "@tarojs/plugin-platform-alipay": "4.0.7", "@tarojs/plugin-platform-h5": "4.0.7", "@tarojs/plugin-platform-jd": "4.0.7", "@tarojs/plugin-platform-qq": "4.0.7", "@tarojs/plugin-platform-swan": "4.0.7", "@tarojs/plugin-platform-tt": "4.0.7", "@tarojs/plugin-platform-weapp": "4.0.7", "@tarojs/router": "4.0.7", "@tarojs/runtime": "4.0.7", "@tarojs/shared": "4.0.7", "@tarojs/taro": "4.0.7", "autoprefixer": "^10.4.21", "axios": "^1.11.0", "postcss": "^8.5.6", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^7.7.1", "tailwindcss": "^4.1.11", "zustand": "^5.0.6"}, "devDependencies": {"@babel/core": "^7.21.0", "@babel/preset-react": "^7.18.6", "@tarojs/cli": "4.0.7", "@tarojs/vite-runner": "4.0.7", "@types/node": "^18.15.0", "@types/react": "^18.0.0", "@types/react-dom": "^19.1.7", "@types/webpack-env": "^1.13.6", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "@vitejs/plugin-react": "^4.0.0", "eslint": "^9.32.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.5.3", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-simple-import-sort": "^12.1.1", "prettier": "^3.6.2", "tsconfig-paths-webpack-plugin": "^4.2.0", "typescript": "^5.0.0", "vite": "^5.0.0"}, "engines": {"node": ">=16"}}